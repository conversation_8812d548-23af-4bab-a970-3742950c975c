package com.kerryprops.kip.audit.integration;

import com.kerryprops.kip.audit.AuditContext;
import com.kerryprops.kip.audit.AuditQueue;
import com.kerryprops.kip.audit.AuditRequestInfo;
import com.kerryprops.kip.audit.XuserInfo;
import org.assertj.core.api.Assertions;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Optional;

import static java.util.concurrent.TimeUnit.SECONDS;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 审计实体集成测试.
 * 测试标注了 @AuditEntity 的实体在 CRUD 操作时是否正确触发审计记录.
 *
 * <AUTHOR>
 */
/**
 * 审计实体集成测试.
 * 测试标注了 @AuditEntity 的实体在 CRUD 操作时是否正确触发审计记录.
 * 当前类的方法不能一起执行，也不建议在maven test 阶段执行，只用于开发人员验证功能是否正常
 *
 * <AUTHOR> Zhang
 */
class AuditEntityIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private TestUserRepository testUserRepository;

    @SpyBean
    private AuditQueue auditQueue;

    private static void setupAuditContext() {
        AuditContext.storeAuditRequestInfo(Instancio.create(AuditRequestInfo.class));
        AuditContext.storeCurrentUser(Instancio.create(XuserInfo.class));
    }

    private static void waitForCompletion() {
        await().atMost(10, SECONDS).pollDelay(1, SECONDS).until(() -> true);
    }

    @BeforeEach
    void setUp() {
        // 清理数据库
        testUserRepository.deleteAll();
        testUserRepository.flush();
        // 重置 mock 状态，清除所有之前的交互记录
        reset(auditQueue);
        // 设置审计上下文
        setupAuditContext();
    }

    @Test
    @DisplayName("创建标注@AuditEntity的实体时应该触发审计记录")
    void whenCreateAuditEntity_shouldTriggerAuditRecord() {
        TestUser user = Instancio.create(TestUser.class);

        testUserRepository.save(user);

    }

    @Test
    @DisplayName("更新标注@AuditEntity的实体时应该触发审计记录")
    void whenUpdateAuditEntity_shouldTriggerAuditRecord() {
        // Given - 首先创建一个实体
        TestUser user = Instancio.create(TestUser.class);
        TestUser savedUser = testUserRepository.saveAndFlush(user);

        // When - 更新实体
        savedUser.setUsername("updated_" + savedUser.getUsername());
        savedUser.setEmail("updated_" + savedUser.getEmail());
        savedUser.setAge(savedUser.getAge() + 1);
        // 触发 postUpdate
        testUserRepository.saveAndFlush(savedUser);

        // Then - 验证审计记录被触发（至少一次更新操作）
        verify(auditQueue, atLeast(1)).enqueue(any());
        waitForCompletion();
    }

    @Test
    @DisplayName("删除标注@AuditEntity的实体时应该触发审计记录")
    void whenDeleteAuditEntity_shouldTriggerAuditRecord() {
        // Given - 首先创建一个实体
        TestUser user = Instancio.create(TestUser.class);
        TestUser savedUser = testUserRepository.saveAndFlush(user);
        // 验证创建操作触发了审计
        verify(auditQueue, atLeastOnce()).enqueue(any());

        // When - 删除实体
        testUserRepository.delete(savedUser);
        testUserRepository.flush();

        // 验证实体确实被删除
        Optional<TestUser> deletedUser = testUserRepository.findById(savedUser.getId());
        Assertions.assertThat(deletedUser)
                .isEmpty();

        waitForCompletion();
    }

    @Test
    @DisplayName("查询标注@AuditEntity的实体时不应该触发审计记录")
    void whenFindAuditEntity_shouldNotTriggerAuditRecord() {
        // Given - 首先创建一个实体
        TestUser user = Instancio.create(TestUser.class);
        TestUser savedUser = testUserRepository.saveAndFlush(user);
        verify(auditQueue, atLeastOnce()).enqueue(any());

        // 等待异步操作完成
        waitForCompletion();
        reset(auditQueue); // 重置mock状态

        // When - 查询实体
        testUserRepository.findById(savedUser.getId());
        testUserRepository.findAll();

        // 短暂等待确保没有延迟的异步操作
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Then - 验证查询操作不会触发审计记录
        verify(auditQueue, never()).enqueue(any());
    }

    @Test
    @DisplayName("批量操作标注@AuditEntity的实体时应该为每个实体触发审计记录")
    void whenBatchOperationsOnAuditEntity_shouldTriggerAuditForEach() {
        // Given - 创建多个实体
        List<TestUser> users = Instancio.ofList(TestUser.class)
                .size(3)
                .create();

        // When - 批量保存
        List<TestUser> savedUsers = testUserRepository.saveAll(users);
        testUserRepository.flush();

        // Then - 验证每个实体都触发了审计记录
        verify(auditQueue, atLeast(3)).enqueue(any());

        // 验证保存成功
        Assertions.assertThat(savedUsers)
                .hasSize(3);

        // When - 批量更新（重置mock以单独验证更新操作）
        reset(auditQueue);
        savedUsers.forEach(user -> {
            user.setEmail("updated_" + user.getEmail());
            user.setAge(user.getAge() + 1);
        });
        testUserRepository.saveAll(savedUsers);
        testUserRepository.flush();

        // Then - 验证更新操作也触发了审计记录
        verify(auditQueue, atLeast(3)).enqueue(any());

        // When - 批量删除（重置mock以单独验证删除操作）
        reset(auditQueue);
        testUserRepository.deleteAll(savedUsers);
        testUserRepository.flush();

        // Then - 验证删除操作也触发了审计记录
        verify(auditQueue, atLeast(3)).enqueue(any());

        waitForCompletion();
    }

    @Test
    @DisplayName("并发操作时每个操作都应该触发审计记录")
    void whenConcurrentOperations_shouldTriggerAuditForEach() throws InterruptedException {
        // Given
        final int threadCount = 5;
        final int operationsPerThread = 2;

        // When - 多线程并发操作
        Thread[] threads = new Thread[threadCount];
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                setupAuditContext();
                for (int j = 0; j < operationsPerThread; j++) {
                    TestUser user = Instancio.create(TestUser.class);
                    user.setUsername("user_" + threadIndex + "_" + j);
                    testUserRepository.save(user);
                }
            });
            threads[i].start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }

        // Then - 验证所有操作都触发了审计记录
        verify(auditQueue, atLeast(threadCount * operationsPerThread)).enqueue(any());

        waitForCompletion();
    }

}